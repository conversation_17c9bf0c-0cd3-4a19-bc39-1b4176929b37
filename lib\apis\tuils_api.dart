import 'package:dio/dio.dart';
import 'package:flutter/gestures.dart';
import '../models/upload_response.dart';
import '../http/dio_instance.dart';
import '../models/base_response.dart';
import '../models/moments_list_response.dart';
import 'package:flutter/material.dart';

class Api {
  static Api instance = Api._();

  Api._();

  // 注册接口
  Future<BaseResponse> register(data) async {
    Response response = await DioInstance.instance().post(path: '/user/registerByPhone', data: data);
    BaseResponse registerResponse = BaseResponse.fromJson(response.data);
    return registerResponse;
  }

  // 上传接口
  Future<Object> upload(data) async {
    Response response = await DioInstance.instance().post(path: '/support/file/upload', data: data);
    UploadResponse uploadResponse = UploadResponse.fromJson(response.data);
    return uploadResponse;
  }
  // 发布朋友圈
  Future<BaseResponse> publish(data) async {
    Response response = await DioInstance.instance().post(path: '/moments/add', data: data);
    BaseResponse publishResponse = BaseResponse.fromJson(response.data);
    return publishResponse;
  }
  // 获取朋友圈列表
  Future<MomentsListResponse> getMomentsList(data) async {
    Response response = await DioInstance.instance().post(path: '/moments/queryPage', data: data);
    MomentsListResponse momentsListResponse = MomentsListResponse.fromJson(response.data);
    return momentsListResponse;
  }

  // 朋友圈点赞
  Future<BaseResponse> like(data) async {
    Response response = await DioInstance.instance().post(path: '/moments/addLike', data: data);
    BaseResponse likeResponse = BaseResponse.fromJson(response.data);
    return likeResponse;
  }
  // 朋友圈取消点赞
  Future<BaseResponse> cancelLike(data) async {
    Response response = await DioInstance.instance().post(path: '/moments/cancelLike', data: data);
    BaseResponse cancelLikeResponse = BaseResponse.fromJson(response.data);
    return cancelLikeResponse;
  }

  // 朋友圈评论
  Future<BaseResponse> comment(data) async {
    Response response = await DioInstance.instance().post(path: '/moments/addComment', data: data);
    BaseResponse commentResponse = BaseResponse.fromJson(response.data);
    return commentResponse;
  }

  // 朋友圈删除评论
  Future<BaseResponse> deleteComment(data) async {
    Response response = await DioInstance.instance().post(path: '/moments/deleteComment', data: data);
    BaseResponse deleteCommentResponse = BaseResponse.fromJson(response.data);
    return deleteCommentResponse;
  }

  // 修改朋友圈封面
  Future<BaseResponse> updateCover(data) async {
    Response response = await DioInstance.instance().post(path: '/user/updateMomentCoverUrl', data: data);
    BaseResponse updateCoverResponse = BaseResponse.fromJson(response.data);
    return updateCoverResponse;
  }

  // 通过手机号查询用户id
  Future<BaseResponse> getUserIDByPhone(String phoneNumber) async {
    Response response = await DioInstance.instance().get(path: '/user/queryId', queryParameters: {'phone': phoneNumber});
    BaseResponse getUserIDByPhoneResponse = BaseResponse.fromJson(response.data);
    return getUserIDByPhoneResponse;
  }

  // 退出登录
  Future<BaseResponse> logout() async {
    Response response = await DioInstance.instance().post(path: '/user/logout');
    BaseResponse logoutResponse = BaseResponse.fromJson(response.data);
    return logoutResponse;
  }
}
