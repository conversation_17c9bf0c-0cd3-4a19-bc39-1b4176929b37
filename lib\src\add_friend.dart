import 'package:flutter/material.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/src/user_profile.dart';
import 'package:tencent_cloud_chat_sdk/manager/v2_tim_manager.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/life_cycle/add_friend_life_cycle.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import 'package:tencent_cloud_chat_demo/apis/tuils_api.dart';



class AddFriend extends StatelessWidget {
  final ValueChanged<V2TimConversation>? directToChat;
  final VoidCallback? closeFunc;

  const AddFriend({Key? key, this.directToChat, this.closeFunc}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TUIKitScreenUtils.getDeviceWidget(
        context: context,
        desktopWidget: TIMUIKitAddFriend(
          closeFunc: closeFunc,
          onTapAlreadyFriendsItem: (String userID) async {
            debugPrint("点击了好友项，用户ID: $userID");
            final V2TIMManager _sdkInstance = TIMUIKitCore.getSDKInstance();
            final conversationID = "c2c_$userID";
            final res = await _sdkInstance.getConversationManager().getConversation(conversationID: conversationID);

            if (res.code == 0) {
              final conversation = res.data ?? V2TimConversation(conversationID: conversationID, userID: userID, type: 1);
              if (directToChat != null) {
                directToChat!(conversation);
              }
            }
          },
        ),
        defaultWidget: Scaffold(
          appBar: AppBar(
            shadowColor: Colors.white,
            title: Text(
              TIM_t("添加好友"),
              style: TextStyle(color: hexToColor("1f2329"), fontSize: 16),
            ),
            backgroundColor: hexToColor("fffffff"),
            surfaceTintColor: hexToColor("fffffff"),
            leading: IconButton(
              padding: const EdgeInsets.only(left: 16),
              icon: Icon(
                Icons.arrow_back_ios,
                color: hexToColor("2a2e35"),
                size: 20,
              ),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
          ),
          body: TIMUIKitAddFriend(
            onTapAlreadyFriendsItem: (String userID) {
              debugPrint("点击了好友项，用户ID: $userID");
              // Navigator.push(
              //     context,
              //     MaterialPageRoute(
              //       builder: (context) => UserProfile(userID: userID),
              //     ));
            },
            lifeCycle: CustomAddFriendLifeCycle(
              onSearch: (phoneNumber){
                // debugPrint("收到搜索请求，手机号: $phoneNumber");
              },
            ),
          ),
        ));
  }
}

class CustomAddFriendLifeCycle extends AddFriendLifeCycle {
    // 添加你需要的属性
  final Function(String)? onSearch;
  final BuildContext? context;
  
  // 自定义构造函数
  CustomAddFriendLifeCycle({
    this.onSearch,
    this.context,
  });

  Future<List<V2TimUserFullInfo>?> searchFriend(String phoneNumber) async {
    // 打印接收到的手机号
    debugPrint("收到搜索请求，手机号: $phoneNumber");
    
    // 调用传入的 onSearch 回调
    if (onSearch != null) {
      onSearch!(phoneNumber);
    }
    
    // 调用你的后台API，通过手机号查询用户ID
    try {
      final response = await Api.instance.getUserIDByPhone(phoneNumber);
      if(response.code == 0 && response.data != null && response.data!.isNotEmpty){
        debugPrint("查询到用户信息: ${response.data}");
        var phone = response.data;
        return [V2TimUserFullInfo(userID: phone)];
      }
    } catch (e) {
      debugPrint("搜索用户时发生错误: $e");
      return [];
    }
  }
}
