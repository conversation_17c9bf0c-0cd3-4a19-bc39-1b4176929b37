// lib/src/pages/find/moments/moments_media_grid.dart

import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; 
import 'package:cached_network_image/cached_network_image.dart';

// 在moments_media_grid.dart中添加以下代码

// 媒体类型枚举
enum MediaType {
  image,
  video
}

// 媒体项模型
class MediaItem {
  final String url;
  final MediaType type;
  final String? thumbnailUrl; // 视频缩略图URL
  final String? duration; // 视频时长

  MediaItem({
    required this.url,
    required this.type,
    this.thumbnailUrl,
    this.duration,
  });
}

class MomentsMediaGrid extends StatelessWidget {
  final List<Map<String, Object>> mediaItems;
  final double spacing = 5.0;
  
  const MomentsMediaGrid({
    Key? key,
    required this.mediaItems,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (mediaItems.isEmpty) {
      return const SizedBox.shrink();
    }
    
    // 单张图片显示
    if (mediaItems.length == 1) {
      return _buildSingleImage(context);
    }
    
    // 多张图片网格显示
    return _buildImageGrid(context);
  }
  
  // 构建单张图片视图
  Widget _buildSingleImage(BuildContext context) {
    return GestureDetector(
      onTap: () => _previewImages(context, 0),
      child: Container(
          width: 128,
          height: 188,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: CachedNetworkImage(
            imageUrl: mediaItems[0]['mediaUrl'].toString(),
            fit: BoxFit.cover,
            placeholder: (context, url) => const SizedBox(
              width: 128,
              height: 188,
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  // 构建图片网格
  Widget _buildImageGrid(BuildContext context) {
    // 确定网格布局
    int crossAxisCount = _getCrossAxisCount();
    
    return Container(
      constraints: const BoxConstraints(
        maxWidth: 300,
      ),
      child: GridView.builder(
        padding: EdgeInsets.zero,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          mainAxisSpacing: spacing,
          crossAxisSpacing: spacing,
        ),
        itemCount: mediaItems.length > 9 ? 9 : mediaItems.length,
        itemBuilder: (context, index) {
          // 如果是第9张且总数超过9张，显示+N
          if (index == 8 && mediaItems.length > 9) {
            return _buildMoreIndicator(context, mediaItems.length - 9);
          }
          
          return GestureDetector(
            onTap: () => _previewImages(context, index),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: CachedNetworkImage(
                imageUrl: mediaItems[index]['mediaUrl'].toString(),
                fit: BoxFit.cover,
                placeholder: (context, url) => const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
  
  // 构建"更多"指示器
  Widget _buildMoreIndicator(BuildContext context, int moreCount) {
    return GestureDetector(
      onTap: () => _previewImages(context, 8),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black45,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: Text(
            '+$moreCount',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
  
  // 根据图片数量确定网格列数
  int _getCrossAxisCount() {
    if (mediaItems.length <= 4) {
      return 2; // 2-4张图片显示2列
    }
    return 3; // 5-9张图片显示3列
  }
  
  // 预览图片
void _previewImages(BuildContext context, int initialIndex) {
  // 使用Navigator.push创建一个全屏页面
  Navigator.of(context).push(
    MaterialPageRoute(
      builder: (context) => Scaffold(
        backgroundColor: Colors.black,
        // 不使用AppBar，而是自定义顶部区域，这样可以更好地控制状态栏
        extendBodyBehindAppBar: true, // 内容延伸到状态栏下方
        appBar: AppBar(
          backgroundColor: Colors.transparent, // 透明背景
          elevation: 0,
          automaticallyImplyLeading: false, // 不显示返回按钮
          actions: [
            // 右上角关闭按钮
            IconButton(
              icon: const Icon(Icons.close, color: Colors.white, size: 30),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        ),
        body: PageView.builder(
          controller: PageController(initialPage: initialIndex),
          itemCount: mediaItems.length,
          itemBuilder: (context, index) {
            return InteractiveViewer(
              minScale: 0.5,
              maxScale: 3.0,
              child: Center(
                child: Image.network(
                  mediaItems[index]['mediaUrl'].toString(),
                  fit: BoxFit.contain,
                ),
              ),
            );
          },
        ),
      ),
    ),
  );
}
}