import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_styled_toast/flutter_styled_toast.dart';
import 'package:provider/provider.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/src/QrcodeWidget.dart';
import 'package:tencent_cloud_chat_demo/src/my_profile.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_self_info_view_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitAddFriend/tim_uikit_send_application.dart';
import '../../../utils/toast.dart';
import '../../provider/login_user_Info.dart';
import '../../provider/theme.dart';
import '../ScanCode.dart';
import 'dart:convert';

class Qrcode extends StatefulWidget {
  final V2TimUserFullInfo userInfo;

  const Qrcode({super.key,required this.userInfo});

  @override
  State<StatefulWidget> createState() => _QrcodeState();
}

class _QrcodeState extends State<Qrcode> {
  final GlobalKey _qrKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    final qrcodeWidget = QrCodeWidget(
      userData: jsonEncode({'userId': widget.userInfo.userID}),
      size: 206,
    );
    return Stack(
      children: [
        AppLogo(),
        // 顶部导航栏
        Positioned(
          top: MediaQuery.of(context).padding.top,
          left: 0,
          right: 0,
          child: Container(
            height: 56,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child:  Row(children: [
                    Icon(
                      Icons.arrow_back_ios,
                      color: Colors.white,
                      size: 20,
                    ),
                    Text(
                      TIM_t('二维码'),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 17,
                        fontWeight: FontWeight.w600,
                      ),
                    )
                  ]),
                ),
                GestureDetector(
                  onTap: () {
                    // 扫一扫
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (con) =>
                                ScanCode(onScanCompleted: (value) {
                                  final Map<String, dynamic> data = jsonDecode(value);
                                  final loginUserInfoModel = Provider.of<LoginUserInfo>(context, listen: false);
                                  final V2TimUserFullInfo loginUserInfo = loginUserInfoModel.loginUserInfo;
                                  debugPrint("扫码结果：${data['userId']} ${loginUserInfo.userID}");
                                  if(loginUserInfo.userID== data['userId']){
                                      //提示
                                    ToastUtils.toast('不能添加自己');
                                    return;
                                  }
                                  final TUISelfInfoViewModel _selfInfoViewModel = serviceLocator<TUISelfInfoViewModel>();

                                  debugPrint("跳转：${data['userId']} ${loginUserInfo.userID}");

                                  Future.delayed(const Duration(seconds: 1), () {
                                      Navigator.pushReplacement(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) => SendApplication(
                                                  friendInfo: widget.userInfo,
                                                  model: _selfInfoViewModel
                                              )
                                          )
                                      );
                                  });
                                  // SendApplication
                                })));
                  },
                  child: Image.asset('assets/scan.png'),
                ),
              ],
            ),
          ),
        ),

        // 主要内容区域
        Center(
          child: Container(
            margin: EdgeInsets.only(
              top: 80,
              left: 24,
              right: 24,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 头像突出效果
                Transform.translate(
                  offset: Offset(0, -50),
                  child: Container(
                    //颜色为白色
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(50), // 宽高的一半
                    ),
                    padding: EdgeInsets.all(7),
                    child: Container(
                        padding: EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(50),
                          gradient: const LinearGradient(
                            begin: Alignment.bottomLeft,
                            end: Alignment.topRight,
                            colors: [
                              Color.fromRGBO(0, 114, 252, 0.7),
                              Color.fromRGBO(0, 114, 252, 0.08), // 你的蓝色
                              Color.fromRGBO(0, 114, 252, 0.0), // 透明
                            ],
                          ),
                        ),
                        child: Container(
                          padding: EdgeInsets.all(3),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(50), // 宽高的一半
                          ),
                          child: Container(
                            padding: EdgeInsets.all(2),
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: Color(0xFF4A90E2),
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 8,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Center(
                              child: Text(
                                widget.userInfo!.nickName![0],
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 32,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        )),
                  ),
                ),
                // 内容区域
                Transform.translate(
                  offset: Offset(0, -50),
                  child: Padding(
                    padding: EdgeInsets.fromLTRB(32, 0, 32, 0),
                    child: Column(
                      children: [
                        // 用户名
                        Text(
                          widget.userInfo.nickName as String,
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        SizedBox(height: 32),
                        // 二维码区域 - 使用图片占位
                        Container(
                          padding: EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Color.fromRGBO(0, 114, 252, 0.4), // 你的蓝色
                                Color.fromRGBO(0, 114, 252, 0.0), // 透明
                              ],
                            ),
                          ),
                          child: Stack(
                            children: [
                              // 二维码图片占位
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: RepaintBoundary(
                                    key: _qrKey,
                                    child: Container(
                                      padding: EdgeInsets.all(16),
                                      color: Colors.white,
                                      child: qrcodeWidget,
                                    ),
                                  ),
                                ),
                              ),
                              // 中心Logo
                            ],
                          ),
                        ),
                        SizedBox(height: 16),
                        // 提示文字
                        Text(

                          TIM_t('使用')+' phpchat '+TIM_t('扫一扫')+','+TIM_t('加我为好友'),
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        SizedBox(height: 32),
                        // 下载按钮
                        Container(
                          width: double.infinity,
                          height: 40,
                          child: ElevatedButton(
                            onPressed: () async {
                              final success = await QrCodeWidget.saveQrCodeFromWidget(_qrKey);
                              if (success) {
                                ToastUtils.toast('二维码已保存到相册');
                              } else {
                                ToastUtils.toast('保存失败请检查权限设置');
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Color(0xFF007AFF),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              elevation: 0,
                            ),
                            child: Text(
                              TIM_t('保存到相册'),
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ],
    );
  }
}
